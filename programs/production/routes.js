export default [
    // Production
    {
        name: 'production',
        title: 'Production',
        icon: 'industry-alt',
        items: [
            {name: 'requests', title: 'Requests'},
            {name: 'requests-detail'},
            {name: 'orders', title: 'Orders'},
            {name: 'orders-detail'},
            {name: 'tasks', title: 'Tasks'},
            {name: 'tasks-detail'},
            {name: 'eco', title: 'ECO'},
            {name: 'eco-detail'},

            {name: 'operations-detail', condition: () => false}
        ]
    },

    // Planning
    {
        name: 'production-planning',
        title: 'Production Planning',
        icon: 'puzzle-piece',
        items: [
            {name: 'aps', title: 'APS - Advanced Planning'},
            {name: 'production-calendar', title: 'Production Calendar'}
        ]
    },

    // Master Data
    {
        name: 'master-data',
        title: 'Master Data',
        icon: 'books',
        items: [
            {name: 'models', title: 'Models'},
            {name: 'models-detail'},
            {name: 'bom', title: 'Bill Of Materials'},
            {name: 'bom-detail'},
            {
                name: 'resources',
                title: 'Resources',
                params: {
                    filters: {
                        scope: 'production'
                    }
                },
                view: 'system.management.configuration.resources.master'
            },
            {
                name: 'resources-detail',
                params: {
                    model: {
                        scope: ['production']
                    }
                },
                view: 'system.management.configuration.resources.detail'
            },
            {
                name: 'products',
                title: 'Products',
                view: 'inventory.catalog.products.master'
            },
            {
                name: 'products-detail',
                view: 'inventory.catalog.products.detail'
            }
        ]
    },

    {
        name: 'reports',
        title: 'Reports',
        icon: 'chart-bar',
        items: [
            {
                name: 'production-orders-analysis',
                title: 'Production Orders Analysis'
            },
            {
                name: 'raw-material-analysis',
                title: 'Input Product Analysis'
            },
            {
                name: 'production-tasks-analysis',
                title: 'Production Tasks Analysis'
            },
            {
                name: 'production-processing-times',
                title: 'Production Processing Times'
            },
            {
                name: 'work-center-analysis',
                title: 'Work Center Analysis'
            },
            {
                name: 'operation-analysis',
                title: 'Operation Analysis'
            },
            {
                name: 'cost-analysis',
                title: 'Cost Analysis'
            },
            {
                name: 'resource-calendar',
                title: 'Resource Calendar',
                view: 'production.production-planning.production-calendar.master',
                params: {forReport: true}
            }
        ]
    },

    // Configuration
    {
        name: 'configuration',
        title: 'Configuration',
        icon: 'wrench',
        items: [
            {name: 'work-centers', title: 'Work Centers'},
            {name: 'work-centers-detail'},
            {
                name: 'working-hours',
                title: 'Working Hours',
                view: 'system.management.configuration.working-hours.master'
            },
            {name: 'working-hours-detail', view: 'system.management.configuration.working-hours.detail'},
            {name: 'pausing-reasons', title: 'Pausing Reasons'},
            {name: 'scrap-reasons', title: 'Scrap Reasons'},
            {name: 'request-types', title: 'Request Types'}
        ]
    }
];
